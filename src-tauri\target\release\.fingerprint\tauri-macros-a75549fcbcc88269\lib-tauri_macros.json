{"rustc": 16591470773350601817, "features": "[\"compression\", \"custom-protocol\", \"shell-scope\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"shell-scope\", \"tracing\"]", "target": 4649449654257170297, "profile": 17984201634715228204, "path": 11523918301111581431, "deps": [[2713742371683562785, "syn", false, 4159513654086052094], [3060637413840920116, "proc_macro2", false, 11804710092911423844], [8292277814562636972, "tauri_utils", false, 17585147619206883858], [13077543566650298139, "heck", false, 12378107339233081810], [17492769205600034078, "tauri_codegen", false, 3059716867095371988], [17990358020177143287, "quote", false, 8139606416633743329]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-macros-a75549fcbcc88269\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}