@echo off
echo ========================================
echo    📤 部署到 GitHub - doubleHao2/audio-monitor
echo ========================================
echo.

echo 📋 部署信息:
echo - GitHub 用户: doubleHao2
echo - 仓库名称: audio-monitor
echo - 分支: main
echo.

echo 🔍 检查 Git 状态...
git status --porcelain > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 当前目录不是 Git 仓库
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

echo ✅ Git 仓库检查通过
echo.

echo 📦 添加所有文件到暂存区...
git add .

echo 📝 提交更改...
set /p commit_msg="请输入提交信息 (直接回车使用默认信息): "
if "%commit_msg%"=="" (
    set commit_msg=feat: 更新项目文件和配置
)

git commit -m "%commit_msg%"

if %errorlevel% neq 0 (
    echo ℹ️  没有新的更改需要提交
) else (
    echo ✅ 提交成功
)

echo.
echo 🚀 推送到 GitHub...
echo ⚠️  首次推送可能需要输入 GitHub 凭据

git push -u origin main

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo    🎉 部署成功！
    echo ========================================
    echo.
    echo 🌐 GitHub 仓库地址:
    echo https://github.com/doubleHao2/audio-monitor
    echo.
    echo 📱 在线演示地址 (需要启用 GitHub Pages):
    echo https://doublehao2.github.io/audio-monitor/standalone.html
    echo.
    echo 💡 下一步操作:
    echo 1. 访问 GitHub 仓库查看代码
    echo 2. 在仓库设置中启用 GitHub Pages
    echo 3. 创建 Release 发布版本
    echo.
) else (
    echo.
    echo ========================================
    echo    ❌ 部署失败
    echo ========================================
    echo.
    echo 可能的原因:
    echo 1. 网络连接问题
    echo 2. GitHub 凭据未配置
    echo 3. 仓库不存在或无权限
    echo.
    echo 💡 解决方案:
    echo 1. 检查网络连接
    echo 2. 配置 Git 凭据: git config --global user.name "你的用户名"
    echo 3. 在 GitHub 上创建 audio-monitor 仓库
    echo.
)

echo.
pause
