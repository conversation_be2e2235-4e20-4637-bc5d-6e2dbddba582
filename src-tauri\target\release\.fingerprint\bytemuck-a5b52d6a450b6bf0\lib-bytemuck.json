{"rustc": 16591470773350601817, "features": "[\"extern_crate_alloc\"]", "declared_features": "[\"aarch64_simd\", \"align_offset\", \"alloc_uninit\", \"avx512_simd\", \"bytemuck_derive\", \"const_zeroed\", \"derive\", \"extern_crate_alloc\", \"extern_crate_std\", \"impl_core_error\", \"latest_stable_rust\", \"min_const_generics\", \"must_cast\", \"must_cast_extra\", \"nightly_docs\", \"nightly_float\", \"nightly_portable_simd\", \"nightly_stdsimd\", \"pod_saturating\", \"track_caller\", \"transparentwrapper_extra\", \"unsound_ptr_pod_impl\", \"wasm_simd\", \"zeroable_atomics\", \"zeroable_maybe_uninit\", \"zeroable_unwind_fn\"]", "target": 5195934831136530909, "profile": 1672261348018318404, "path": 16419945880723651975, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\bytemuck-a5b52d6a450b6bf0\\dep-lib-bytemuck", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}