{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"codegen\", \"config-json5\", \"config-toml\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 16335202704479430798, "path": 18341358067483675665, "deps": [[4450062412064442726, "dirs_next", false, 12781047883446462085], [4899080583175475170, "semver", false, 1833552965442421552], [7468248713591957673, "cargo_toml", false, 633659169625847463], [8292277814562636972, "tauri_utils", false, 4763354764566324088], [9689903380558560274, "serde", false, 13356758597005812676], [10301936376833819828, "json_patch", false, 11168370189291401301], [13077543566650298139, "heck", false, 3901096866868070764], [13625485746686963219, "anyhow", false, 9739582024938941695], [14189313126492979171, "tauri_winres", false, 10066063329227064362], [15367738274754116744, "serde_json", false, 441750760351105765], [15622660310229662834, "walkdir", false, 3859886730018907805]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-29ee8bb98761122e\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}