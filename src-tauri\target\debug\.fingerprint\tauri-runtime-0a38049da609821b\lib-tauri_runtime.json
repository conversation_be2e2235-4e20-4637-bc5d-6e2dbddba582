{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 10306386172444932100, "profile": 5144538945173598434, "path": 8380590053004193717, "deps": [[3150220818285335163, "url", false, 9444140677483369912], [4381063397040571828, "webview2_com", false, 4631337162424091932], [4405182208873388884, "http", false, 10878387370901701545], [7653476968652377684, "windows", false, 14668577320041509417], [8008191657135824715, "thiserror", false, 12277593154533700771], [8292277814562636972, "tauri_utils", false, 4337947901565456909], [8319709847752024821, "uuid", false, 10328342596123032847], [8866577183823226611, "http_range", false, 14412563020474178742], [9689903380558560274, "serde", false, 13356758597005812676], [11693073011723388840, "raw_window_handle", false, 981689866408228079], [13208667028893622512, "rand", false, 11879403667527116706], [14162324460024849578, "build_script_build", false, 8312400297366764395], [15367738274754116744, "serde_json", false, 14253702188518516403]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-0a38049da609821b\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}