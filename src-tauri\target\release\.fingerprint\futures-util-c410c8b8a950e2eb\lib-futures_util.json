{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 10671129054187407251, "path": 3303257178207977090, "deps": [[1615478164327904835, "pin_utils", false, 18201744832890764682], [1906322745568073236, "pin_project_lite", false, 2389355156100224855], [5451793922601807560, "slab", false, 14929152764931222035], [7620660491849607393, "futures_core", false, 428085969174735394], [10565019901765856648, "futures_macro", false, 12377974745239688574], [16240732885093539806, "futures_task", false, 8229819807746054797]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-util-c410c8b8a950e2eb\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}