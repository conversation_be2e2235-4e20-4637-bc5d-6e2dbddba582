name: Build and Release

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    strategy:
      matrix:
        platform: [windows-latest]

    runs-on: ${{ matrix.platform }}

    steps:
    - name: Checkout repository
      uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Setup Rust
      uses: dtolnay/rust-toolchain@stable

    - name: Install dependencies
      run: npm ci

    - name: Build web assets
      run: npm run build:web

    - name: Build Tauri app
      run: npm run tauri:build

    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: tauri-app-${{ matrix.platform }}
        path: |
          src-tauri/target/release/*.exe
          src-tauri/target/release/bundle/

  release:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')

    steps:
    - name: Download artifacts
      uses: actions/download-artifact@v3

    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        files: |
          **/*.exe
          **/*.msi
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
