{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"codegen\", \"config-json5\", \"config-toml\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 3829077726856309774, "path": 18341358067483675665, "deps": [[4450062412064442726, "dirs_next", false, 10659208037290453441], [4899080583175475170, "semver", false, 5213702282947731756], [7468248713591957673, "cargo_toml", false, 3556388475273779018], [8292277814562636972, "tauri_utils", false, 17585147619206883858], [9689903380558560274, "serde", false, 15984095930558835974], [10301936376833819828, "json_patch", false, 2690966267822985377], [13077543566650298139, "heck", false, 12378107339233081810], [13625485746686963219, "anyhow", false, 17049156702159294058], [14189313126492979171, "tauri_winres", false, 10648254468431951421], [15367738274754116744, "serde_json", false, 907440240443594353], [15622660310229662834, "walkdir", false, 2024185238712419512]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-build-855cd0d83a02105b\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}