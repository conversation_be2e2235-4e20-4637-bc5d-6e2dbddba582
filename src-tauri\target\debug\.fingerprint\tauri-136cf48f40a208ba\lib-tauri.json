{"rustc": 16591470773350601817, "features": "[\"compression\", \"default\", \"objc-exception\", \"open\", \"protocol-asset\", \"regex\", \"shell-open\", \"shell-open-api\", \"system-tray\", \"tauri-runtime-wry\", \"window-close\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"wry\"]", "declared_features": "[\"api-all\", \"app-all\", \"app-hide\", \"app-show\", \"base64\", \"bytes\", \"clap\", \"cli\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-confirm\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"dox\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-extract-api\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-multipart\", \"http-request\", \"ico\", \"icon-ico\", \"icon-png\", \"indexmap\", \"infer\", \"isolation\", \"linux-protocol-headers\", \"macos-private-api\", \"minisign-verify\", \"native-tls-vendored\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"os_pipe\", \"path-all\", \"png\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"reqwest-client\", \"reqwest-native-tls-vendored\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"sys-locale\", \"system-tray\", \"tauri-runtime-wry\", \"test\", \"time\", \"tracing\", \"updater\", \"win7-notifications\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-data-url\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-closable\", \"window-set-content-protected\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-ignore-cursor-events\", \"window-set-max-size\", \"window-set-maximizable\", \"window-set-min-size\", \"window-set-minimizable\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"windows7-compat\", \"wry\", \"zip\"]", "target": 12223948975794516716, "profile": 5144538945173598434, "path": 14051902342798935117, "deps": [[40386456601120721, "percent_encoding", false, 9903042276772267709], [1260461579271933187, "serialize_to_javascript", false, 12775160656785861777], [1441306149310335789, "tempfile", false, 7701874558688150008], [3150220818285335163, "url", false, 9444140677483369912], [3722963349756955755, "once_cell", false, 9072542997806263575], [3988549704697787137, "open", false, 13169020481110340234], [4381063397040571828, "webview2_com", false, 4631337162424091932], [4405182208873388884, "http", false, 10878387370901701545], [4450062412064442726, "dirs_next", false, 12781047883446462085], [4899080583175475170, "semver", false, 7474859038544447637], [5180608563399064494, "tauri_macros", false, 4529670585462085089], [5610773616282026064, "build_script_build", false, 4586906468021069322], [5986029879202738730, "log", false, 8555561636712512847], [7653476968652377684, "windows", false, 14668577320041509417], [8008191657135824715, "thiserror", false, 12277593154533700771], [8292277814562636972, "tauri_utils", false, 4337947901565456909], [8319709847752024821, "uuid", false, 10328342596123032847], [9451456094439810778, "regex", false, 13066986688969473203], [9538054652646069845, "tokio", false, 2227938305436128809], [9623796893764309825, "ignore", false, 2921170266283787376], [9689903380558560274, "serde", false, 13356758597005812676], [9920160576179037441, "getrandom", false, 9001965057602181653], [10629569228670356391, "futures_util", false, 18214163840747849970], [11601763207901161556, "tar", false, 16545647402003592796], [11693073011723388840, "raw_window_handle", false, 981689866408228079], [11989259058781683633, "dunce", false, 10230067452156376648], [12986574360607194341, "serde_repr", false, 17984368083826069945], [13208667028893622512, "rand", false, 11879403667527116706], [13625485746686963219, "anyhow", false, 9739582024938941695], [14162324460024849578, "tauri_runtime", false, 7278455030057802393], [14564311161534545801, "encoding_rs", false, 5032541734336780516], [15367738274754116744, "serde_json", false, 14253702188518516403], [16228250612241359704, "tauri_runtime_wry", false, 11712219290776781029], [17155886227862585100, "glob", false, 8808646221422140041], [17278893514130263345, "state", false, 16927588936346241249], [17772299992546037086, "flate2", false, 17401029828542448626]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-136cf48f40a208ba\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}