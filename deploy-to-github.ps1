# 部署到 GitHub 脚本
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    📤 部署到 GitHub - doubleHao2/audio-monitor" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "📋 部署信息:" -ForegroundColor Yellow
Write-Host "- GitHub 用户: doubleHao2" -ForegroundColor White
Write-Host "- 仓库名称: audio-monitor" -ForegroundColor White
Write-Host "- 分支: main" -ForegroundColor White
Write-Host ""

Write-Host "🔍 检查 Git 状态..." -ForegroundColor Yellow

try {
    $gitStatus = git status --porcelain 2>$null
    Write-Host "✅ Git 仓库检查通过" -ForegroundColor Green
} catch {
    Write-Host "❌ 当前目录不是 Git 仓库" -ForegroundColor Red
    Write-Host "请确保在项目根目录运行此脚本" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

Write-Host ""
Write-Host "📦 添加所有文件到暂存区..." -ForegroundColor Yellow
git add .

Write-Host "📝 提交更改..." -ForegroundColor Yellow
$commitMsg = Read-Host "请输入提交信息 (直接回车使用默认信息)"
if ([string]::IsNullOrWhiteSpace($commitMsg)) {
    $commitMsg = "feat: 更新项目文件和配置"
}

try {
    git commit -m $commitMsg
    Write-Host "✅ 提交成功" -ForegroundColor Green
} catch {
    Write-Host "ℹ️  没有新的更改需要提交" -ForegroundColor Blue
}

Write-Host ""
Write-Host "🚀 推送到 GitHub..." -ForegroundColor Yellow
Write-Host "⚠️  首次推送可能需要输入 GitHub 凭据" -ForegroundColor Yellow

try {
    git push -u origin main
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "    🎉 部署成功！" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "🌐 GitHub 仓库地址:" -ForegroundColor Cyan
    Write-Host "https://github.com/doubleHao2/audio-monitor" -ForegroundColor White
    Write-Host ""
    Write-Host "📱 在线演示地址 (需要启用 GitHub Pages):" -ForegroundColor Cyan
    Write-Host "https://doublehao2.github.io/audio-monitor/standalone.html" -ForegroundColor White
    Write-Host ""
    Write-Host "💡 下一步操作:" -ForegroundColor Yellow
    Write-Host "1. 访问 GitHub 仓库查看代码" -ForegroundColor White
    Write-Host "2. 在仓库设置中启用 GitHub Pages" -ForegroundColor White
    Write-Host "3. 创建 Release 发布版本" -ForegroundColor White
    Write-Host ""
    
    $openRepo = Read-Host "🌐 是否立即打开 GitHub 仓库？(Y/N)"
    if ($openRepo -eq "Y" -or $openRepo -eq "y") {
        Start-Process "https://github.com/doubleHao2/audio-monitor"
    }
    
} catch {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "    ❌ 部署失败" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Red
    Write-Host ""
    Write-Host "可能的原因:" -ForegroundColor Yellow
    Write-Host "1. 网络连接问题" -ForegroundColor White
    Write-Host "2. GitHub 凭据未配置" -ForegroundColor White
    Write-Host "3. 仓库不存在或无权限" -ForegroundColor White
    Write-Host ""
    Write-Host "💡 解决方案:" -ForegroundColor Cyan
    Write-Host "1. 检查网络连接" -ForegroundColor White
    Write-Host "2. 配置 Git 凭据: git config --global user.name `"你的用户名`"" -ForegroundColor White
    Write-Host "3. 在 GitHub 上创建 audio-monitor 仓库" -ForegroundColor White
    Write-Host ""
}

Write-Host ""
Read-Host "按任意键退出"
