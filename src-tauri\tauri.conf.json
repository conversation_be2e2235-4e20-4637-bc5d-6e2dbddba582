{"build": {"beforeDevCommand": "", "beforeBuildCommand": "", "devPath": "../", "distDir": "../"}, "package": {"productName": "音频监听器", "version": "1.0.0"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true}, "window": {"all": false, "close": true, "hide": true, "show": true, "maximize": true, "minimize": true, "unmaximize": true, "unminimize": true, "startDragging": true}, "protocol": {"asset": true, "assetScope": ["**"]}}, "bundle": {"active": true, "targets": "all", "identifier": "com.audiomonitor.app", "icon": []}, "security": {"csp": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; media-src 'self' blob: data:"}, "windows": [{"fullscreen": false, "resizable": true, "title": "音频监听器", "width": 500, "height": 600, "minWidth": 400, "minHeight": 500}]}}