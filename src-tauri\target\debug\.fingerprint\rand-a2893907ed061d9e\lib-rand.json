{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 15657897354478470176, "path": 17281646005809502401, "deps": [[1573238666360410412, "rand_chacha", false, 10815340572904452433], [18130209639506977569, "rand_core", false, 3002401708064976835]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-a2893907ed061d9e\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}