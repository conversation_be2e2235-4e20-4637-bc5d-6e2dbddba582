{"rustc": 16591470773350601817, "features": "[\"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 11250625435679592442, "path": 14936214404469579842, "deps": [[7911289239703230891, "adler2", false, 7075611793567431126]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\miniz_oxide-2a14f3ce6c945672\\dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}