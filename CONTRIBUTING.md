# 🤝 贡献指南

感谢你对音频监听器项目的关注！我们欢迎各种形式的贡献。

## 🚀 快速开始

### 开发环境设置

1. **克隆仓库**
   ```bash
   git clone https://github.com/doubleHao2/audio-monitor.git
   cd audio-monitor
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动开发服务器**
   ```bash
   npm run tauri:dev
   ```

### 项目结构

```
audio-monitor/
├── src-tauri/          # Rust 后端代码
├── dist/               # 构建输出目录
├── index.html          # 主界面
├── main.js             # 核心逻辑
├── standalone.html     # 浏览器版本
└── test.html          # 功能测试页面
```

## 📝 贡献类型

### 🐛 Bug 报告
- 使用 GitHub Issues 报告 bug
- 提供详细的重现步骤
- 包含系统信息和错误日志

### ✨ 功能请求
- 在 Issues 中描述新功能
- 说明使用场景和预期效果
- 讨论实现方案

### 🔧 代码贡献
- Fork 项目并创建功能分支
- 遵循现有代码风格
- 添加必要的测试
- 提交 Pull Request

## 📋 开发规范

### 代码风格
- **JavaScript**: 使用 ES6+ 语法
- **Rust**: 遵循 Rust 官方风格指南
- **HTML/CSS**: 保持简洁和语义化

### 提交信息
```
类型(范围): 简短描述

详细描述（可选）

关闭 #issue_number（如果适用）
```

类型：
- `feat`: 新功能
- `fix`: Bug 修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建/工具相关

### 分支命名
- `feature/功能名称`
- `bugfix/问题描述`
- `hotfix/紧急修复`

## 🧪 测试

### 运行测试
```bash
# 浏览器版本测试
open test.html

# 功能测试
npm run tauri:dev
```

### 测试清单
- [ ] 麦克风权限获取
- [ ] 音量检测准确性
- [ ] 系统托盘功能
- [ ] 窗口显示/隐藏
- [ ] 阈值调整
- [ ] 错误处理

## 📦 构建和发布

### 本地构建
```bash
npm run build:web
npm run tauri:build
```

### 发布流程
1. 更新版本号
2. 更新 CHANGELOG
3. 创建 Git 标签
4. GitHub Actions 自动构建

## 🆘 获取帮助

- 📖 查看 [README.md](README.md)
- 🐛 提交 [GitHub Issues](https://github.com/doubleHao2/audio-monitor/issues)
- 💬 参与 [Discussions](https://github.com/doubleHao2/audio-monitor/discussions)

## 📄 许可证

通过贡献代码，你同意你的贡献将在 [MIT License](LICENSE) 下发布。
