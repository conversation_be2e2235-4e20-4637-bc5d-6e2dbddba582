# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Tauri build outputs
src-tauri/target/
src-tauri/Cargo.lock

# Build outputs
dist/
build/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Temporary folders
tmp/
temp/

# Windows specific
*.exe.config
*.exe.manifest

# Rust specific
**/*.rs.bk
Cargo.lock

# Tauri specific
src-tauri/target/
src-tauri/gen/
