{"rustc": 16591470773350601817, "features": "[\"file-drop\", \"objc-exception\", \"protocol\", \"tray\"]", "declared_features": "[\"default\", \"devtools\", \"dox\", \"file-drop\", \"fullscreen\", \"linux-headers\", \"objc-exception\", \"protocol\", \"tracing\", \"transparent\", \"tray\"]", "target": 2463569863749872413, "profile": 5325659981465705816, "path": 2790465224785257298, "deps": [[3007252114546291461, "tao", false, 5724065891360798150], [3150220818285335163, "url", false, 433971801140220136], [3540822385484940109, "windows_implement", false, 259239507088486698], [3722963349756955755, "once_cell", false, 2454724538139820284], [4381063397040571828, "webview2_com", false, 17472851839045778868], [4405182208873388884, "http", false, 7095023862650205754], [4684437522915235464, "libc", false, 4627090042858946481], [5986029879202738730, "log", false, 6521534687954369588], [7653476968652377684, "windows", false, 16694896786816741222], [8008191657135824715, "thiserror", false, 9382702652342440483], [8391357152270261188, "build_script_build", false, 4399976796524266653], [9689903380558560274, "serde", false, 9464942257113468766], [11989259058781683633, "dunce", false, 17194864312650271351], [15367738274754116744, "serde_json", false, 1864518245595142160]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\wry-c50c231cb0e1abd8\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}