{"name": "@tauri-apps/cli", "version": "1.6.3", "description": "Command line interface for building Tauri apps", "funding": {"type": "opencollective", "url": "https://opencollective.com/tauri"}, "repository": {"type": "git", "url": "git+https://github.com/tauri-apps/tauri.git"}, "contributors": ["Tauri Team <<EMAIL>> (https://tauri.app)"], "license": "Apache-2.0 OR MIT", "bugs": {"url": "https://github.com/tauri-apps/tauri/issues"}, "homepage": "https://github.com/tauri-apps/tauri#readme", "publishConfig": {"access": "public"}, "main": "main.js", "types": "main.d.ts", "napi": {"name": "cli", "triples": {"additional": ["aarch64-apple-darwin", "aarch64-unknown-linux-gnu", "aarch64-unknown-linux-musl", "armv7-unknown-linux-gnueabihf", "x86_64-unknown-linux-musl", "i686-pc-windows-msvc", "aarch64-pc-windows-msvc"]}}, "devDependencies": {"@napi-rs/cli": "2.16.1", "cross-env": "7.0.3", "cross-spawn": "7.0.3", "fs-extra": "11.1.1", "jest": "29.7.0", "jest-transform-toml": "1.0.0", "prettier": "2.8.8"}, "resolutions": {"semver": ">=7.5.2", "braces": "3.0.3"}, "engines": {"node": ">= 10"}, "bin": {"tauri": "./tauri.js"}, "scripts": {"artifacts": "napi artifacts", "build:release": "cross-env TARGET=node napi build --platform --release", "build": "cross-env TARGET=node napi build --platform", "prepublishOnly": "napi prepublish -t npm --gh-release-id $RELEASE_ID", "prepack": "cp ../schema.json .", "test": "jest --runInBand --forceExit --no-cache", "version": "napi version", "tauri": "node ./tauri.js", "format": "prettier --write ./package.json ./tauri.js", "format:check": "prettier --check ./package.json ./tauri.js"}, "dependencies": {"semver": ">=7.5.2"}, "optionalDependencies": {"@tauri-apps/cli-win32-x64-msvc": "1.6.3", "@tauri-apps/cli-darwin-x64": "1.6.3", "@tauri-apps/cli-linux-x64-gnu": "1.6.3", "@tauri-apps/cli-darwin-arm64": "1.6.3", "@tauri-apps/cli-linux-arm64-gnu": "1.6.3", "@tauri-apps/cli-linux-arm64-musl": "1.6.3", "@tauri-apps/cli-linux-arm-gnueabihf": "1.6.3", "@tauri-apps/cli-linux-x64-musl": "1.6.3", "@tauri-apps/cli-win32-ia32-msvc": "1.6.3", "@tauri-apps/cli-win32-arm64-msvc": "1.6.3"}}