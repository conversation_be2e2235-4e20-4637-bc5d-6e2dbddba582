{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\", \"regex\", \"shell-scope\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\", \"shell-scope\"]", "target": 17460618180909919773, "profile": 17984201634715228204, "path": 9204307398723947112, "deps": [[3060637413840920116, "proc_macro2", false, 11804710092911423844], [4899080583175475170, "semver", false, 5213702282947731756], [7392050791754369441, "ico", false, 12593177069754474419], [8008191657135824715, "thiserror", false, 16789402978291365194], [8292277814562636972, "tauri_utils", false, 17585147619206883858], [8319709847752024821, "uuid", false, 16665683630190296139], [9451456094439810778, "regex", false, 7562714372266134230], [9689903380558560274, "serde", false, 15984095930558835974], [9857275760291862238, "sha2", false, 10866204662556586242], [10301936376833819828, "json_patch", false, 2690966267822985377], [12687914511023397207, "png", false, 4011422620533263514], [14132538657330703225, "brotli", false, 12506537657699052943], [15367738274754116744, "serde_json", false, 907440240443594353], [15622660310229662834, "walkdir", false, 2024185238712419512], [17990358020177143287, "quote", false, 8139606416633743329], [18066890886671768183, "base64", false, 2320702328749237810]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-codegen-21d2fb496e379f31\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}