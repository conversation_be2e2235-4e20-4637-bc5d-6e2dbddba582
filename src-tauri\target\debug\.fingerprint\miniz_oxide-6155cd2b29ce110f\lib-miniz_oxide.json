{"rustc": 16591470773350601817, "features": "[\"default\", \"simd\", \"simd-adler32\", \"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 9346826069578435451, "path": 14936214404469579842, "deps": [[4018467389006652250, "simd_adler32", false, 14598196107953021507], [7911289239703230891, "adler2", false, 7075611793567431126]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\miniz_oxide-6155cd2b29ce110f\\dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}