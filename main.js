class AudioMonitor {
    constructor() {
        this.audioContext = null;
        this.microphone = null;
        this.analyser = null;
        this.dataArray = null;
        this.isMonitoring = false;
        this.threshold = 30; // 默认阈值 30%
        this.isSpeaking = false;
        this.speakingTimeout = null;
        
        this.initializeElements();
        this.bindEvents();
    }

    initializeElements() {
        this.statusElement = document.getElementById('status');
        this.volumeFillElement = document.getElementById('volumeFill');
        this.startBtn = document.getElementById('startBtn');
        this.stopBtn = document.getElementById('stopBtn');
        this.thresholdSlider = document.getElementById('threshold');
        this.thresholdValueElement = document.getElementById('thresholdValue');
    }

    bindEvents() {
        this.startBtn.addEventListener('click', () => this.startMonitoring());
        this.stopBtn.addEventListener('click', () => this.stopMonitoring());
        
        this.thresholdSlider.addEventListener('input', (e) => {
            this.threshold = parseInt(e.target.value);
            this.thresholdValueElement.textContent = this.threshold;
        });
    }

    async startMonitoring() {
        try {
            // 请求麦克风权限
            const stream = await navigator.mediaDevices.getUserMedia({ 
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                } 
            });

            // 创建音频上下文
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.microphone = this.audioContext.createMediaStreamSource(stream);
            this.analyser = this.audioContext.createAnalyser();

            // 配置分析器
            this.analyser.fftSize = 256;
            this.analyser.smoothingTimeConstant = 0.8;
            
            const bufferLength = this.analyser.frequencyBinCount;
            this.dataArray = new Uint8Array(bufferLength);

            // 连接音频节点
            this.microphone.connect(this.analyser);

            this.isMonitoring = true;
            this.updateStatus('idle', '正在监听...');
            this.startBtn.disabled = true;
            this.stopBtn.disabled = false;

            // 开始分析音频
            this.analyzeAudio();

        } catch (error) {
            console.error('获取麦克风权限失败:', error);
            this.updateStatus('error', '无法访问麦克风，请检查权限设置');
        }
    }

    stopMonitoring() {
        this.isMonitoring = false;
        
        if (this.audioContext) {
            this.audioContext.close();
            this.audioContext = null;
        }

        if (this.microphone) {
            this.microphone.disconnect();
            this.microphone = null;
        }

        this.updateStatus('idle', '监听已停止');
        this.volumeFillElement.style.width = '0%';
        this.startBtn.disabled = false;
        this.stopBtn.disabled = true;

        // 清除说话状态
        if (this.speakingTimeout) {
            clearTimeout(this.speakingTimeout);
            this.speakingTimeout = null;
        }
        this.isSpeaking = false;
    }

    analyzeAudio() {
        if (!this.isMonitoring) return;

        // 获取音频数据
        this.analyser.getByteFrequencyData(this.dataArray);

        // 计算音量 (RMS)
        let sum = 0;
        for (let i = 0; i < this.dataArray.length; i++) {
            sum += this.dataArray[i] * this.dataArray[i];
        }
        const rms = Math.sqrt(sum / this.dataArray.length);
        const volume = Math.min(100, (rms / 128) * 100); // 转换为百分比

        // 更新音量条
        this.volumeFillElement.style.width = volume + '%';

        // 检测是否在说话
        if (volume > this.threshold) {
            if (!this.isSpeaking) {
                this.isSpeaking = true;
                this.updateStatus('speaking', '正在说话...');
            }
            
            // 重置超时
            if (this.speakingTimeout) {
                clearTimeout(this.speakingTimeout);
            }
            
            // 设置延迟，避免短暂停顿就切换状态
            this.speakingTimeout = setTimeout(() => {
                this.isSpeaking = false;
                this.updateStatus('idle', '正在监听...');
            }, 500); // 500ms 延迟
            
        }

        // 继续分析
        requestAnimationFrame(() => this.analyzeAudio());
    }

    updateStatus(type, message) {
        this.statusElement.className = `status ${type}`;
        this.statusElement.textContent = message;
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new AudioMonitor();
});

// Tauri API 集成（如果需要的话）
if (window.__TAURI__) {
    const { invoke } = window.__TAURI__.tauri;
    
    // 可以在这里添加与 Rust 后端的通信
    console.log('Tauri API 可用');
}
