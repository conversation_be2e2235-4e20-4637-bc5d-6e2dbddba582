{"rustc": 16591470773350601817, "features": "[\"objc-exception\", \"system-tray\"]", "declared_features": "[\"arboard\", \"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"linux-headers\", \"macos-private-api\", \"objc-exception\", \"system-tray\", \"tracing\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 1055146233920268666, "deps": [[4381063397040571828, "webview2_com", false, 4631337162424091932], [7653476968652377684, "windows", false, 14668577320041509417], [8292277814562636972, "tauri_utils", false, 4337947901565456909], [8319709847752024821, "uuid", false, 10328342596123032847], [8391357152270261188, "wry", false, 2167970384275589277], [11693073011723388840, "raw_window_handle", false, 981689866408228079], [13208667028893622512, "rand", false, 11879403667527116706], [14162324460024849578, "tauri_runtime", false, 7278455030057802393], [16228250612241359704, "build_script_build", false, 15642685461107737594]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-3165b789e3fde0d0\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}