<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频监听器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 400px;
            width: 100%;
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2em;
        }

        .status {
            font-size: 1.2em;
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .status.idle {
            background-color: #e8f5e8;
            color: #2d5a2d;
            border: 2px solid #4caf50;
        }

        .status.speaking {
            background-color: #fff3cd;
            color: #856404;
            border: 2px solid #ffc107;
            animation: pulse 1s infinite;
        }

        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 2px solid #dc3545;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .volume-bar {
            width: 100%;
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }

        .volume-fill {
            height: 100%;
            background: linear-gradient(90deg, #4caf50, #ffeb3b, #ff9800, #f44336);
            width: 0%;
            transition: width 0.1s ease;
            border-radius: 10px;
        }

        .controls {
            margin-top: 30px;
        }

        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            margin: 0 10px;
            transition: transform 0.2s ease;
        }

        button:hover {
            transform: translateY(-2px);
        }

        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .threshold-control {
            margin: 20px 0;
        }

        .threshold-control label {
            display: block;
            margin-bottom: 10px;
            color: #333;
        }

        .threshold-control input {
            width: 100%;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 音频监听器</h1>
        
        <div id="status" class="status idle">
            等待开始监听...
        </div>

        <div class="volume-bar">
            <div id="volumeFill" class="volume-fill"></div>
        </div>

        <div class="threshold-control">
            <label for="threshold">音量阈值: <span id="thresholdValue">30</span>%</label>
            <input type="range" id="threshold" min="1" max="100" value="30">
        </div>

        <div class="controls">
            <button id="startBtn">开始监听</button>
            <button id="stopBtn" disabled>停止监听</button>
        </div>
    </div>

    <script src="main.js"></script>
</body>
</html>
