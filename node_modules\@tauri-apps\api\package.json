{"name": "@tauri-apps/api", "version": "1.6.0", "description": "Tauri API definitions", "funding": {"type": "opencollective", "url": "https://opencollective.com/tauri"}, "repository": {"type": "git", "url": "git+https://github.com/tauri-apps/tauri.git"}, "contributors": ["Tauri Programme within The Commons Conservancy"], "license": "Apache-2.0 OR MIT", "bugs": {"url": "https://github.com/tauri-apps/tauri/issues"}, "homepage": "https://github.com/tauri-apps/tauri#readme", "type": "module", "main": "./index.cjs", "module": "./index.js", "exports": {".": {"import": "./index.js", "require": "./index.cjs"}, "./*": {"import": "./*.js", "require": "./*.cjs"}, "./package.json": "./package.json"}, "scripts": {"build": "rollup -c --configPlugin typescript", "npm-pack": "yarn build && cd ./dist && npm pack", "npm-publish": "yarn build && cd ./dist && yarn publish --access public --loglevel silly", "ts:check": "tsc -noEmit", "lint": "eslint --ext ts \"./src/**/*.ts\"", "lint:fix": "eslint --fix --ext ts \"./src/**/*.ts\"", "format": "prettier --write . --config ../../.prettierrc --ignore-path .gitignore --ignore-path ../../.prettierignore", "format:check": "prettier --check . --config ../../.prettierrc --ignore-path .gitignore --ignore-path ../../.prettierignore"}, "devDependencies": {"@rollup/plugin-terser": "0.4.4", "@rollup/plugin-typescript": "11.1.5", "@types/node": "20.10.5", "@typescript-eslint/eslint-plugin": "5.62.0", "eslint-config-standard-with-typescript": "34.0.1", "@typescript-eslint/parser": "5.62.0", "eslint": "8.56.0", "eslint-config-prettier": "8.10.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-n": "15.7.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "6.1.1", "eslint-plugin-security": "1.7.1", "fast-glob": "3.3.2", "prettier": "3.1.1", "rollup": "3.29.4", "typescript": "5.3.3"}, "engines": {"node": ">= 14.6.0", "npm": ">= 6.6.0", "yarn": ">= 1.19.1"}}