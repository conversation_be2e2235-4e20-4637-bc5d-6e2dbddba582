{"rustc": 16591470773350601817, "features": "[\"system-tray\"]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 10306386172444932100, "profile": 14831015333953822190, "path": 8380590053004193717, "deps": [[3150220818285335163, "url", false, 433971801140220136], [4381063397040571828, "webview2_com", false, 17472851839045778868], [4405182208873388884, "http", false, 7095023862650205754], [7653476968652377684, "windows", false, 16694896786816741222], [8008191657135824715, "thiserror", false, 9382702652342440483], [8292277814562636972, "tauri_utils", false, 157594433550355900], [8319709847752024821, "uuid", false, 7491926078516526095], [8866577183823226611, "http_range", false, 7452187035804456759], [9689903380558560274, "serde", false, 9464942257113468766], [11693073011723388840, "raw_window_handle", false, 15589677163586950001], [13208667028893622512, "rand", false, 4152676194360715696], [14162324460024849578, "build_script_build", false, 14362523366790304791], [15367738274754116744, "serde_json", false, 1864518245595142160]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-238ac3fd090d13a3\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}