{"rustc": 16591470773350601817, "features": "[\"compression\", \"shell-scope\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"shell-scope\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 11523918301111581431, "deps": [[2713742371683562785, "syn", false, 12825818981731931738], [3060637413840920116, "proc_macro2", false, 3722225902823197376], [8292277814562636972, "tauri_utils", false, 4763354764566324088], [13077543566650298139, "heck", false, 3901096866868070764], [17492769205600034078, "tauri_codegen", false, 14745498426620671072], [17990358020177143287, "quote", false, 15042609247914122772]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-3bd1b380855af1a9\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}