{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17415290695940690668, "build_script_build", false, 11566693253408192875]], "local": [{"RerunIfChanged": {"output": "debug\\build\\audio-monitor-5464f0b885488f3f\\output", "paths": ["tauri.conf.json"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": "{ \"build\": { \"devPath\": \"http://127.0.0.1:1430\" } }"}}], "rustflags": [], "config": 0, "compile_kind": 0}