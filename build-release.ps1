# 音频监听器 - 生产版本构建脚本
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    🚀 音频监听器 - 生产版本构建" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "📋 构建信息:" -ForegroundColor Yellow
Write-Host "- 应用名称: 音频监听器" -ForegroundColor White
Write-Host "- 版本: 1.0.0" -ForegroundColor White
Write-Host "- 构建类型: Release (生产版本)" -ForegroundColor White
Write-Host ""

Write-Host "🧹 清理之前的构建..." -ForegroundColor Yellow
if (Test-Path "src-tauri\target\release") {
    Remove-Item -Recurse -Force "src-tauri\target\release"
    Write-Host "✅ 清理完成" -ForegroundColor Green
} else {
    Write-Host "ℹ️  无需清理" -ForegroundColor Blue
}
Write-Host ""

Write-Host "🔨 开始构建生产版本..." -ForegroundColor Yellow
Write-Host "⚠️  这可能需要几分钟时间，请耐心等待..." -ForegroundColor Yellow
Write-Host ""

# 记录开始时间
$startTime = Get-Date

try {
    # 执行构建
    npm run tauri:build
    
    if ($LASTEXITCODE -eq 0) {
        $endTime = Get-Date
        $duration = $endTime - $startTime
        
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "    🎉 构建成功！" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host ""
        Write-Host "⏱️  构建耗时: $($duration.Minutes) 分 $($duration.Seconds) 秒" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "📦 生成的文件位置:" -ForegroundColor Yellow
        Write-Host "- 安装程序: src-tauri\target\release\bundle\msi\" -ForegroundColor White
        Write-Host "- 可执行文件: src-tauri\target\release\audio-monitor.exe" -ForegroundColor White
        Write-Host ""
        Write-Host "💡 提示:" -ForegroundColor Cyan
        Write-Host "- 安装程序可以分发给其他用户" -ForegroundColor White
        Write-Host "- 可执行文件可以直接运行，无需安装" -ForegroundColor White
        Write-Host ""
        
        # 检查文件大小
        if (Test-Path "src-tauri\target\release\audio-monitor.exe") {
            $fileSize = (Get-Item "src-tauri\target\release\audio-monitor.exe").Length
            $fileSizeMB = [math]::Round($fileSize / 1MB, 2)
            Write-Host "📊 可执行文件大小: $fileSizeMB MB" -ForegroundColor Cyan
            Write-Host ""
            
            $runChoice = Read-Host "🚀 是否立即运行生产版本？(Y/N)"
            if ($runChoice -eq "Y" -or $runChoice -eq "y") {
                Write-Host "启动应用..." -ForegroundColor Green
                Start-Process "src-tauri\target\release\audio-monitor.exe"
            }
        }
        
        # 检查安装程序
        $msiPath = Get-ChildItem -Path "src-tauri\target\release\bundle\msi\" -Filter "*.msi" -ErrorAction SilentlyContinue | Select-Object -First 1
        if ($msiPath) {
            $msiSize = [math]::Round($msiPath.Length / 1MB, 2)
            Write-Host "📦 安装程序大小: $msiSize MB" -ForegroundColor Cyan
            Write-Host "📁 安装程序路径: $($msiPath.FullName)" -ForegroundColor White
        }
        
    } else {
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Red
        Write-Host "    ❌ 构建失败" -ForegroundColor Red
        Write-Host "========================================" -ForegroundColor Red
        Write-Host ""
        Write-Host "请检查上面的错误信息并修复问题后重试。" -ForegroundColor Yellow
        Write-Host ""
    }
} catch {
    Write-Host ""
    Write-Host "❌ 构建过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
}

Write-Host ""
Read-Host "按任意键退出"
