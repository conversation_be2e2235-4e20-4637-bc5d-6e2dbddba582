C:\Users\<USER>\Documents\augment-projects\test\src-tauri\target\release\deps\libbytemuck-a5b52d6a450b6bf0.rmeta: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\allocation.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\anybitpattern.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\checked.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\internal.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\zeroable.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\zeroable_in_option.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\pod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\pod_in_option.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\no_uninit.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\contiguous.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\offset_of.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\transparent.rs

C:\Users\<USER>\Documents\augment-projects\test\src-tauri\target\release\deps\libbytemuck-a5b52d6a450b6bf0.rlib: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\allocation.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\anybitpattern.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\checked.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\internal.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\zeroable.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\zeroable_in_option.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\pod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\pod_in_option.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\no_uninit.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\contiguous.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\offset_of.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\transparent.rs

C:\Users\<USER>\Documents\augment-projects\test\src-tauri\target\release\deps\bytemuck-a5b52d6a450b6bf0.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\allocation.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\anybitpattern.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\checked.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\internal.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\zeroable.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\zeroable_in_option.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\pod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\pod_in_option.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\no_uninit.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\contiguous.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\offset_of.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\transparent.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\lib.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\allocation.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\anybitpattern.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\checked.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\internal.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\zeroable.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\zeroable_in_option.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\pod.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\pod_in_option.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\no_uninit.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\contiguous.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\offset_of.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bytemuck-1.23.1\src\transparent.rs:
