{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\", \"glob\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"glob\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"toml\", \"walkdir\"]", "target": 7530130812022395703, "profile": 5325659981465705816, "path": 5011004359846547994, "deps": [[561782849581144631, "html5ever", false, 13957330890898028317], [3150220818285335163, "url", false, 433971801140220136], [3334271191048661305, "windows_version", false, 8600775503899115961], [4899080583175475170, "semver", false, 1525762717963971202], [5578504951057029730, "serde_with", false, 614444843154830770], [5986029879202738730, "log", false, 6521534687954369588], [6262254372177975231, "kuchiki", false, 1005617528404305355], [6606131838865521726, "ctor", false, 15507169177575559773], [6997837210367702832, "infer", false, 16815056936512771578], [8008191657135824715, "thiserror", false, 9382702652342440483], [9689903380558560274, "serde", false, 9464942257113468766], [10301936376833819828, "json_patch", false, 14070335944626404851], [11989259058781683633, "dunce", false, 17194864312650271351], [14132538657330703225, "brotli", false, 4731052178775098558], [15367738274754116744, "serde_json", false, 1864518245595142160], [15622660310229662834, "walkdir", false, 17263293371568802960], [15932120279885307830, "memchr", false, 11594422652850079461], [17155886227862585100, "glob", false, 2528361041703392655], [17186037756130803222, "phf", false, 16171985991055942402]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-a70b89927d12feeb\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}