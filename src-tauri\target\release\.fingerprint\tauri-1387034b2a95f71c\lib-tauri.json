{"rustc": 16591470773350601817, "features": "[\"compression\", \"custom-protocol\", \"default\", \"objc-exception\", \"open\", \"protocol-asset\", \"regex\", \"shell-open\", \"shell-open-api\", \"system-tray\", \"tauri-runtime-wry\", \"window-close\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"wry\"]", "declared_features": "[\"api-all\", \"app-all\", \"app-hide\", \"app-show\", \"base64\", \"bytes\", \"clap\", \"cli\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-confirm\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"dox\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-extract-api\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-multipart\", \"http-request\", \"ico\", \"icon-ico\", \"icon-png\", \"indexmap\", \"infer\", \"isolation\", \"linux-protocol-headers\", \"macos-private-api\", \"minisign-verify\", \"native-tls-vendored\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"os_pipe\", \"path-all\", \"png\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"reqwest-client\", \"reqwest-native-tls-vendored\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"sys-locale\", \"system-tray\", \"tauri-runtime-wry\", \"test\", \"time\", \"tracing\", \"updater\", \"win7-notifications\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-data-url\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-closable\", \"window-set-content-protected\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-ignore-cursor-events\", \"window-set-max-size\", \"window-set-maximizable\", \"window-set-min-size\", \"window-set-minimizable\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"windows7-compat\", \"wry\", \"zip\"]", "target": 12223948975794516716, "profile": 14831015333953822190, "path": 14051902342798935117, "deps": [[40386456601120721, "percent_encoding", false, 2683537286950056235], [1260461579271933187, "serialize_to_javascript", false, 8225361281892847732], [1441306149310335789, "tempfile", false, 14836790580471056080], [3150220818285335163, "url", false, 433971801140220136], [3722963349756955755, "once_cell", false, 2454724538139820284], [3988549704697787137, "open", false, 14288404335128018341], [4381063397040571828, "webview2_com", false, 17472851839045778868], [4405182208873388884, "http", false, 7095023862650205754], [4450062412064442726, "dirs_next", false, 1363059227819504838], [4899080583175475170, "semver", false, 1525762717963971202], [5180608563399064494, "tauri_macros", false, 15360690206776913962], [5610773616282026064, "build_script_build", false, 18216891722051050919], [5986029879202738730, "log", false, 6521534687954369588], [7653476968652377684, "windows", false, 16694896786816741222], [8008191657135824715, "thiserror", false, 9382702652342440483], [8292277814562636972, "tauri_utils", false, 157594433550355900], [8319709847752024821, "uuid", false, 7491926078516526095], [9451456094439810778, "regex", false, 12583657637631892731], [9538054652646069845, "tokio", false, 5662736456956170276], [9623796893764309825, "ignore", false, 4592570255784085617], [9689903380558560274, "serde", false, 9464942257113468766], [9920160576179037441, "getrandom", false, 1491932130767156979], [10629569228670356391, "futures_util", false, 4801838303812658025], [11601763207901161556, "tar", false, 9768892917237975777], [11693073011723388840, "raw_window_handle", false, 15589677163586950001], [11989259058781683633, "dunce", false, 17194864312650271351], [12986574360607194341, "serde_repr", false, 8659394800395879327], [13208667028893622512, "rand", false, 4152676194360715696], [13625485746686963219, "anyhow", false, 9055018873795390290], [14162324460024849578, "tauri_runtime", false, 8699834518108062016], [14564311161534545801, "encoding_rs", false, 2056358842032964121], [15367738274754116744, "serde_json", false, 1864518245595142160], [16228250612241359704, "tauri_runtime_wry", false, 2805683353113249888], [17155886227862585100, "glob", false, 2528361041703392655], [17278893514130263345, "state", false, 6903102941658238368], [17772299992546037086, "flate2", false, 1533030753367788293]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-1387034b2a95f71c\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}