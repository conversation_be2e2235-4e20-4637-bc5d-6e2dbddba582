<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频监听器 - 独立版本</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 400px;
            width: 100%;
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2em;
        }

        .status {
            font-size: 1.2em;
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .status.idle {
            background-color: #e8f5e8;
            color: #2d5a2d;
            border: 2px solid #4caf50;
        }

        .status.speaking {
            background-color: #fff3cd;
            color: #856404;
            border: 2px solid #ffc107;
            animation: pulse 1s infinite;
        }

        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 2px solid #dc3545;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .volume-bar {
            width: 100%;
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }

        .volume-fill {
            height: 100%;
            background: linear-gradient(90deg, #4caf50, #ffeb3b, #ff9800, #f44336);
            width: 0%;
            transition: width 0.1s ease;
            border-radius: 10px;
        }

        .controls {
            margin-top: 30px;
        }

        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            margin: 0 10px;
            transition: transform 0.2s ease;
        }

        button:hover {
            transform: translateY(-2px);
        }

        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .threshold-control {
            margin: 20px 0;
        }

        .threshold-control label {
            display: block;
            margin-bottom: 10px;
            color: #333;
        }

        .threshold-control input {
            width: 100%;
            margin-bottom: 10px;
        }

        .volume-display {
            margin: 10px 0;
            font-size: 1.1em;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 音频监听器</h1>
        
        <div id="status" class="status idle">
            等待开始监听...
        </div>

        <div class="volume-bar">
            <div id="volumeFill" class="volume-fill"></div>
        </div>

        <div class="volume-display">
            当前音量: <span id="volumeValue">0</span>%
        </div>

        <div class="threshold-control">
            <label for="threshold">音量阈值: <span id="thresholdValue">30</span>%</label>
            <input type="range" id="threshold" min="1" max="100" value="30">
        </div>

        <div class="controls">
            <button id="startBtn">开始监听</button>
            <button id="stopBtn" disabled>停止监听</button>
        </div>
    </div>

    <script>
        class AudioMonitor {
            constructor() {
                this.audioContext = null;
                this.microphone = null;
                this.analyser = null;
                this.dataArray = null;
                this.isMonitoring = false;
                this.threshold = 30;
                this.isSpeaking = false;
                this.speakingTimeout = null;
                
                this.initializeElements();
                this.bindEvents();
            }

            initializeElements() {
                this.statusElement = document.getElementById('status');
                this.volumeFillElement = document.getElementById('volumeFill');
                this.volumeValueElement = document.getElementById('volumeValue');
                this.startBtn = document.getElementById('startBtn');
                this.stopBtn = document.getElementById('stopBtn');
                this.thresholdSlider = document.getElementById('threshold');
                this.thresholdValueElement = document.getElementById('thresholdValue');
            }

            bindEvents() {
                this.startBtn.addEventListener('click', () => this.startMonitoring());
                this.stopBtn.addEventListener('click', () => this.stopMonitoring());
                
                this.thresholdSlider.addEventListener('input', (e) => {
                    this.threshold = parseInt(e.target.value);
                    this.thresholdValueElement.textContent = this.threshold;
                });
            }

            async startMonitoring() {
                try {
                    // 请求麦克风权限
                    const stream = await navigator.mediaDevices.getUserMedia({ 
                        audio: {
                            echoCancellation: true,
                            noiseSuppression: true,
                            autoGainControl: true
                        } 
                    });

                    // 创建音频上下文
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    this.microphone = this.audioContext.createMediaStreamSource(stream);
                    this.analyser = this.audioContext.createAnalyser();

                    // 配置分析器
                    this.analyser.fftSize = 256;
                    this.analyser.smoothingTimeConstant = 0.8;
                    
                    const bufferLength = this.analyser.frequencyBinCount;
                    this.dataArray = new Uint8Array(bufferLength);

                    // 连接音频节点
                    this.microphone.connect(this.analyser);

                    this.isMonitoring = true;
                    this.updateStatus('idle', '正在监听...');
                    this.startBtn.disabled = true;
                    this.stopBtn.disabled = false;

                    // 开始分析音频
                    this.analyzeAudio();

                } catch (error) {
                    console.error('获取麦克风权限失败:', error);
                    this.updateStatus('error', '无法访问麦克风，请检查权限设置');
                }
            }

            stopMonitoring() {
                this.isMonitoring = false;
                
                if (this.audioContext) {
                    this.audioContext.close();
                    this.audioContext = null;
                }

                if (this.microphone) {
                    this.microphone.disconnect();
                    this.microphone = null;
                }

                this.updateStatus('idle', '监听已停止');
                this.volumeFillElement.style.width = '0%';
                this.volumeValueElement.textContent = '0';
                this.startBtn.disabled = false;
                this.stopBtn.disabled = true;

                // 清除说话状态
                if (this.speakingTimeout) {
                    clearTimeout(this.speakingTimeout);
                    this.speakingTimeout = null;
                }
                this.isSpeaking = false;
            }

            analyzeAudio() {
                if (!this.isMonitoring) return;

                // 获取音频数据
                this.analyser.getByteFrequencyData(this.dataArray);

                // 计算音量 (RMS)
                let sum = 0;
                for (let i = 0; i < this.dataArray.length; i++) {
                    sum += this.dataArray[i] * this.dataArray[i];
                }
                const rms = Math.sqrt(sum / this.dataArray.length);
                const volume = Math.min(100, (rms / 128) * 100);

                // 更新显示
                this.volumeFillElement.style.width = volume + '%';
                this.volumeValueElement.textContent = Math.round(volume);

                // 检测是否在说话
                if (volume > this.threshold) {
                    if (!this.isSpeaking) {
                        this.isSpeaking = true;
                        this.updateStatus('speaking', '正在说话...');
                    }
                    
                    // 重置超时
                    if (this.speakingTimeout) {
                        clearTimeout(this.speakingTimeout);
                    }
                    
                    // 设置延迟，避免短暂停顿就切换状态
                    this.speakingTimeout = setTimeout(() => {
                        this.isSpeaking = false;
                        this.updateStatus('idle', '正在监听...');
                    }, 500);
                }

                // 继续分析
                requestAnimationFrame(() => this.analyzeAudio());
            }

            updateStatus(type, message) {
                this.statusElement.className = `status ${type}`;
                this.statusElement.textContent = message;
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new AudioMonitor();
        });
    </script>
</body>
</html>
