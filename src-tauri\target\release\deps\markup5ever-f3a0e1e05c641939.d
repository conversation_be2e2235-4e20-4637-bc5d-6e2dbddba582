C:\Users\<USER>\Documents\augment-projects\test\src-tauri\target\release\deps\libmarkup5ever-f3a0e1e05c641939.rmeta: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\data\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\tree_builder.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\serialize.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\buffer_queue.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\smallcharset.rs C:\Users\<USER>\Documents\augment-projects\test\src-tauri\target\release\build\markup5ever-2ca1a630ab922f0a\out/generated.rs C:\Users\<USER>\Documents\augment-projects\test\src-tauri\target\release\build\markup5ever-2ca1a630ab922f0a\out/named_entities.rs

C:\Users\<USER>\Documents\augment-projects\test\src-tauri\target\release\deps\libmarkup5ever-f3a0e1e05c641939.rlib: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\data\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\tree_builder.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\serialize.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\buffer_queue.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\smallcharset.rs C:\Users\<USER>\Documents\augment-projects\test\src-tauri\target\release\build\markup5ever-2ca1a630ab922f0a\out/generated.rs C:\Users\<USER>\Documents\augment-projects\test\src-tauri\target\release\build\markup5ever-2ca1a630ab922f0a\out/named_entities.rs

C:\Users\<USER>\Documents\augment-projects\test\src-tauri\target\release\deps\markup5ever-f3a0e1e05c641939.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\data\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\tree_builder.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\serialize.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\buffer_queue.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\smallcharset.rs C:\Users\<USER>\Documents\augment-projects\test\src-tauri\target\release\build\markup5ever-2ca1a630ab922f0a\out/generated.rs C:\Users\<USER>\Documents\augment-projects\test\src-tauri\target\release\build\markup5ever-2ca1a630ab922f0a\out/named_entities.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\lib.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\data\mod.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\mod.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\tree_builder.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\serialize.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\buffer_queue.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\smallcharset.rs:
C:\Users\<USER>\Documents\augment-projects\test\src-tauri\target\release\build\markup5ever-2ca1a630ab922f0a\out/generated.rs:
C:\Users\<USER>\Documents\augment-projects\test\src-tauri\target\release\build\markup5ever-2ca1a630ab922f0a\out/named_entities.rs:

# env-dep:OUT_DIR=C:\\Users\\<USER>\\Documents\\augment-projects\\test\\src-tauri\\target\\release\\build\\markup5ever-2ca1a630ab922f0a\\out
