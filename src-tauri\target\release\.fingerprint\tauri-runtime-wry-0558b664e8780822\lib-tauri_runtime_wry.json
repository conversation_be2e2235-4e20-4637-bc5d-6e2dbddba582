{"rustc": 16591470773350601817, "features": "[\"objc-exception\", \"system-tray\"]", "declared_features": "[\"arboard\", \"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"linux-headers\", \"macos-private-api\", \"objc-exception\", \"system-tray\", \"tracing\"]", "target": 1901661049345253480, "profile": 5325659981465705816, "path": 1055146233920268666, "deps": [[4381063397040571828, "webview2_com", false, 17472851839045778868], [7653476968652377684, "windows", false, 16694896786816741222], [8292277814562636972, "tauri_utils", false, 157594433550355900], [8319709847752024821, "uuid", false, 7491926078516526095], [8391357152270261188, "wry", false, 2116999836640356643], [11693073011723388840, "raw_window_handle", false, 15589677163586950001], [13208667028893622512, "rand", false, 4152676194360715696], [14162324460024849578, "tauri_runtime", false, 8699834518108062016], [16228250612241359704, "build_script_build", false, 15423860779650353323]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-0558b664e8780822\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}