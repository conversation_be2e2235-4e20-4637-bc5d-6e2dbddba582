# 音频监听器启动脚本
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    🎤 音频监听器 - 启动脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "检查环境..." -ForegroundColor Yellow
Write-Host ""

# 检查 Node.js
try {
    $nodeVersion = node --version 2>$null
    Write-Host "✅ Node.js 已安装: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js 未安装，请先安装 Node.js" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查 Rust
try {
    $rustVersion = rustc --version 2>$null
    Write-Host "✅ Rust 已安装: $rustVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Rust 未安装或未配置" -ForegroundColor Red
    Write-Host "正在尝试配置 Rust..." -ForegroundColor Yellow
    rustup default stable
}

Write-Host ""
Write-Host "选择启动方式：" -ForegroundColor Cyan
Write-Host "1. 浏览器版本（推荐，立即可用）" -ForegroundColor White
Write-Host "2. Tauri 桌面版本（需要编译）" -ForegroundColor White
Write-Host "3. 安装 Visual Studio Build Tools（Tauri 必需）" -ForegroundColor White
Write-Host "4. 退出" -ForegroundColor White
Write-Host ""

$choice = Read-Host "请输入选择 (1-4)"

switch ($choice) {
    "1" {
        Write-Host ""
        Write-Host "🌐 启动浏览器版本..." -ForegroundColor Green
        Start-Process "standalone.html"
        Write-Host "✅ 已在浏览器中打开应用" -ForegroundColor Green
        Write-Host "💡 如果没有自动打开，请手动打开 standalone.html 文件" -ForegroundColor Yellow
    }
    "2" {
        Write-Host ""
        Write-Host "🔨 启动 Tauri 开发服务器..." -ForegroundColor Green
        Write-Host "⚠️  首次启动可能需要较长时间编译" -ForegroundColor Yellow
        Write-Host "⚠️  如果出现链接器错误，请选择选项 3 安装 Build Tools" -ForegroundColor Yellow
        Write-Host ""
        npm run tauri:dev
    }
    "3" {
        Write-Host ""
        Write-Host "📥 打开 Visual Studio Build Tools 下载页面..." -ForegroundColor Green
        Start-Process "https://visualstudio.microsoft.com/visual-cpp-build-tools/"
        Write-Host "请下载并安装 Visual Studio Build Tools" -ForegroundColor Yellow
        Write-Host "安装时选择 'C++ build tools' 工作负载" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "或者使用 Chocolatey 快速安装：" -ForegroundColor Cyan
        Write-Host "choco install visualstudio2022buildtools --package-parameters `"--add Microsoft.VisualStudio.Workload.VCTools`"" -ForegroundColor Gray
    }
    "4" {
        Write-Host "👋 再见！" -ForegroundColor Green
        exit 0
    }
    default {
        Write-Host "❌ 无效选择，请重新运行脚本" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
}

Write-Host ""
Read-Host "按任意键退出"
