@echo off
echo ========================================
echo    🎤 音频监听器 - 启动脚本
echo ========================================
echo.

echo 检查环境...
echo.

:: 检查 Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js
    pause
    exit /b 1
) else (
    echo ✅ Node.js 已安装
)

:: 检查 Rust
rustc --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Rust 未安装或未配置
    echo 正在尝试配置 Rust...
    rustup default stable
) else (
    echo ✅ Rust 已安装
)

echo.
echo 选择启动方式：
echo 1. 浏览器版本（推荐，立即可用）
echo 2. Tauri 桌面版本（需要编译）
echo 3. 退出
echo.

set /p choice="请输入选择 (1-3): "

if "%choice%"=="1" (
    echo.
    echo 🌐 启动浏览器版本...
    start "" "standalone.html"
    echo ✅ 已在浏览器中打开应用
    echo 💡 如果没有自动打开，请手动打开 standalone.html 文件
) else if "%choice%"=="2" (
    echo.
    echo 🔨 启动 Tauri 开发服务器...
    echo ⚠️  首次启动可能需要较长时间编译
    echo.
    npm run tauri:dev
) else if "%choice%"=="3" (
    echo 👋 再见！
    exit /b 0
) else (
    echo ❌ 无效选择，请重新运行脚本
    pause
    exit /b 1
)

echo.
pause
