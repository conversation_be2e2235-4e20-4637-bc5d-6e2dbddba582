{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 3303257178207977090, "deps": [[1615478164327904835, "pin_utils", false, 12096768787333181728], [1906322745568073236, "pin_project_lite", false, 5517959609193328240], [5451793922601807560, "slab", false, 9871437668454032372], [7620660491849607393, "futures_core", false, 10927281888925237000], [10565019901765856648, "futures_macro", false, 6772284888554160324], [16240732885093539806, "futures_task", false, 15605073868113214427]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-a9abd2392a55a1e3\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}