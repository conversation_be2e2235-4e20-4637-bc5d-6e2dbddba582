{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\", \"regex\", \"shell-scope\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\", \"shell-scope\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 9204307398723947112, "deps": [[3060637413840920116, "proc_macro2", false, 3722225902823197376], [4899080583175475170, "semver", false, 1833552965442421552], [7392050791754369441, "ico", false, 16824647959953010829], [8008191657135824715, "thiserror", false, 12277593154533700771], [8292277814562636972, "tauri_utils", false, 4763354764566324088], [8319709847752024821, "uuid", false, 10328342596123032847], [9451456094439810778, "regex", false, 6794824585717175984], [9689903380558560274, "serde", false, 13356758597005812676], [9857275760291862238, "sha2", false, 6306212642859392580], [10301936376833819828, "json_patch", false, 11168370189291401301], [12687914511023397207, "png", false, 4671443912394630389], [14132538657330703225, "brotli", false, 3004841465102247802], [15367738274754116744, "serde_json", false, 441750760351105765], [15622660310229662834, "walkdir", false, 3859886730018907805], [17990358020177143287, "quote", false, 15042609247914122772], [18066890886671768183, "base64", false, 8417967864405591777]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-908c003dcce9b4f1\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}