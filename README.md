# 🎤 音频监听器 - Tauri 桌面应用

一个使用 Tauri + JavaScript 开发的桌面应用，实现麦克风音频监听和音量检测功能。

## ✨ 功能特点

- 🎯 **实时音频监听** - 获取麦克风权限，监听音频流
- 📊 **音量检测** - 实时分析音频幅度变化
- 🗣️ **说话状态检测** - 当音量超过阈值时显示"正在说话..."
- 🎛️ **可调节阈值** - 用户可自定义音量检测敏感度
- 📈 **可视化显示** - 实时音量条和数值显示
- 🎨 **美观界面** - 现代化 UI 设计，渐变背景和动画效果

## 🚀 快速开始

### 方式一：立即体验（推荐）

直接打开 `standalone.html` 文件在浏览器中体验所有功能：

```bash
# 在浏览器中打开
file:///path/to/your/project/standalone.html
```

### 方式二：Tauri 桌面应用

#### 前置要求

1. **Node.js** (已安装 ✅)
2. **Rust** (已安装 ✅)
3. **Visual Studio Build Tools** (需要安装)

#### 安装 Visual Studio Build Tools

Tauri 在 Windows 上需要 Visual Studio Build Tools 来编译 Rust 代码：

1. 下载 [Visual Studio Build Tools](https://visualstudio.microsoft.com/visual-cpp-build-tools/)
2. 安装时选择 "C++ build tools" 工作负载
3. 确保包含 "Windows 10/11 SDK" 和 "MSVC v143 编译器工具集"

或者使用 Chocolatey 快速安装：
```powershell
# 以管理员身份运行 PowerShell
choco install visualstudio2022buildtools --package-parameters "--add Microsoft.VisualStudio.Workload.VCTools"
```

#### 运行开发服务器

```bash
# 安装依赖（已完成）
npm install

# 启动开发服务器
npm run tauri:dev
```

#### 构建生产版本

```bash
# 构建桌面应用
npm run tauri:build
```

## 📁 项目结构

```
audio-monitor/
├── index.html              # 主界面 HTML
├── main.js                 # 音频监听逻辑
├── standalone.html         # 独立浏览器版本
├── package.json           # Node.js 依赖
├── src-tauri/             # Tauri 后端
│   ├── Cargo.toml         # Rust 依赖
│   ├── tauri.conf.json    # Tauri 配置
│   ├── build.rs           # 构建脚本
│   └── src/
│       └── main.rs        # Rust 主程序
└── README.md              # 项目说明
```

## 🔧 核心技术实现

### 音频监听

使用 Web Audio API 获取麦克风权限并创建音频分析器：

```javascript
const stream = await navigator.mediaDevices.getUserMedia({ 
    audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
    } 
});

this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
this.microphone = this.audioContext.createMediaStreamSource(stream);
this.analyser = this.audioContext.createAnalyser();
```

### 音量检测算法

使用 RMS (Root Mean Square) 算法计算音频音量：

```javascript
this.analyser.getByteFrequencyData(this.dataArray);

let sum = 0;
for (let i = 0; i < this.dataArray.length; i++) {
    sum += this.dataArray[i] * this.dataArray[i];
}
const rms = Math.sqrt(sum / this.dataArray.length);
const volume = Math.min(100, (rms / 128) * 100);
```

### 说话状态检测

智能检测说话状态，避免短暂停顿造成的状态切换：

```javascript
if (volume > this.threshold) {
    if (!this.isSpeaking) {
        this.isSpeaking = true;
        this.updateStatus('speaking', '正在说话...');
    }
    
    // 500ms 延迟避免频繁切换
    this.speakingTimeout = setTimeout(() => {
        this.isSpeaking = false;
        this.updateStatus('idle', '正在监听...');
    }, 500);
}
```

## 🎛️ 使用说明

1. **开始监听** - 点击"开始监听"按钮，允许麦克风权限
2. **调整阈值** - 使用滑块调整音量检测的敏感度（1-100%）
3. **观察状态** - 说话时状态会变为"正在说话..."并有动画效果
4. **停止监听** - 点击"停止监听"按钮结束监听

## 🔍 故障排除

### 麦克风权限问题
- 确保浏览器/应用有麦克风权限
- 检查系统麦克风设备是否正常工作
- 在浏览器中需要 HTTPS 或 localhost 环境

### Tauri 构建问题
- 确保安装了 Visual Studio Build Tools
- 重启终端以刷新环境变量
- 尝试运行 `rustup update` 更新 Rust

### 音量检测不准确
- 调整阈值滑块到合适的值
- 检查麦克风音量设置
- 确保环境相对安静

## 🛠️ 开发说明

### 添加新功能
- 前端逻辑在 `main.js` 中修改
- UI 样式在 `index.html` 的 `<style>` 部分修改
- Tauri 配置在 `src-tauri/tauri.conf.json` 中修改

### 调试
- 浏览器版本：使用浏览器开发者工具
- Tauri 版本：查看终端输出和 Rust 编译信息

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！
