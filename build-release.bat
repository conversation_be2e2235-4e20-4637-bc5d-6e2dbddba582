@echo off
echo ========================================
echo    🚀 音频监听器 - 生产版本构建
echo ========================================
echo.

echo 📋 构建信息:
echo - 应用名称: 音频监听器
echo - 版本: 1.0.0
echo - 构建类型: Release (生产版本)
echo.

echo 🧹 清理之前的构建...
if exist "src-tauri\target\release" (
    rmdir /s /q "src-tauri\target\release"
    echo ✅ 清理完成
) else (
    echo ℹ️  无需清理
)
echo.

echo 🔨 开始构建生产版本...
echo ⚠️  这可能需要几分钟时间，请耐心等待...
echo.

npm run tauri:build

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo    🎉 构建成功！
    echo ========================================
    echo.
    echo 📦 生成的文件位置:
    echo - 安装程序: src-tauri\target\release\bundle\msi\
    echo - 可执行文件: src-tauri\target\release\audio-monitor.exe
    echo.
    echo 💡 提示:
    echo - 安装程序可以分发给其他用户
    echo - 可执行文件可以直接运行，无需安装
    echo.
    
    if exist "src-tauri\target\release\audio-monitor.exe" (
        echo 🚀 是否立即运行生产版本？
        set /p run_choice="输入 Y 运行，任意键跳过: "
        if /i "%run_choice%"=="Y" (
            echo 启动应用...
            start "" "src-tauri\target\release\audio-monitor.exe"
        )
    )
) else (
    echo.
    echo ========================================
    echo    ❌ 构建失败
    echo ========================================
    echo.
    echo 请检查上面的错误信息并修复问题后重试。
    echo.
)

echo.
pause
