[package]
name = "audio-monitor"
version = "1.0.0"
description = "A Tauri App for monitoring audio input"
authors = ["you"]
license = ""
repository = ""
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1.5", features = [], default-features = false }

[dependencies]
tauri = { version = "1.5", features = [ "system-tray", "window-close", "window-unminimize", "window-start-dragging", "window-maximize", "window-show", "protocol-asset", "window-hide", "shell-open", "window-unmaximize", "window-minimize"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]

[profile.release]
# 生产版本优化
opt-level = "s"          # 优化大小
lto = true              # 链接时优化
codegen-units = 1       # 减少代码生成单元以提高优化
panic = "abort"         # 减少二进制大小
strip = true            # 移除调试符号
