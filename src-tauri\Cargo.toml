[package]
name = "audio-monitor"
version = "1.0.0"
description = "A Tauri App for monitoring audio input"
authors = ["you"]
license = ""
repository = ""
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1.5", features = [], default-features = false }

[dependencies]
tauri = { version = "1.5", features = [ "window-close", "window-unminimize", "window-start-dragging", "window-maximize", "window-show", "protocol-asset", "window-hide", "shell-open", "window-unmaximize", "window-minimize"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
