{"rustc": 16591470773350601817, "features": "[\"file-drop\", \"objc-exception\", \"protocol\"]", "declared_features": "[\"default\", \"devtools\", \"dox\", \"file-drop\", \"fullscreen\", \"linux-headers\", \"objc-exception\", \"protocol\", \"tracing\", \"transparent\", \"tray\"]", "target": 2463569863749872413, "profile": 15657897354478470176, "path": 2790465224785257298, "deps": [[3007252114546291461, "tao", false, 17641922499887959150], [3150220818285335163, "url", false, 9444140677483369912], [3540822385484940109, "windows_implement", false, 11645205037284534123], [3722963349756955755, "once_cell", false, 9072542997806263575], [4381063397040571828, "webview2_com", false, 4631337162424091932], [4405182208873388884, "http", false, 10878387370901701545], [4684437522915235464, "libc", false, 16836430201784941864], [5986029879202738730, "log", false, 8555561636712512847], [7653476968652377684, "windows", false, 14668577320041509417], [8008191657135824715, "thiserror", false, 12277593154533700771], [8391357152270261188, "build_script_build", false, 12429757401442186927], [9689903380558560274, "serde", false, 13356758597005812676], [11989259058781683633, "dunce", false, 10230067452156376648], [15367738274754116744, "serde_json", false, 14253702188518516403]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wry-6fe6ae48b46a66c3\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}