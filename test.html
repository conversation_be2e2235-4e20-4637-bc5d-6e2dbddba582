<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频监听器 - 功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .volume-display {
            font-size: 1.2em;
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🎤 音频监听器 - 功能测试</h1>
    
    <div class="test-section">
        <h2>1. 浏览器兼容性检测</h2>
        <div id="compatibility-status"></div>
    </div>

    <div class="test-section">
        <h2>2. 麦克风权限测试</h2>
        <button id="test-mic">测试麦克风权限</button>
        <div id="mic-status"></div>
    </div>

    <div class="test-section">
        <h2>3. 音频分析测试</h2>
        <button id="start-analysis">开始音频分析</button>
        <button id="stop-analysis" disabled>停止分析</button>
        <div id="analysis-status"></div>
        <div id="volume-display" class="volume-display">音量: 0%</div>
    </div>

    <div class="test-section">
        <h2>4. 完整应用测试</h2>
        <p>如果上述测试都通过，可以打开完整应用：</p>
        <button onclick="window.open('standalone.html', '_blank')">打开完整应用</button>
        <button onclick="window.open('index.html', '_blank')">打开 Tauri 版本</button>
    </div>

    <script>
        // 1. 兼容性检测
        function checkCompatibility() {
            const status = document.getElementById('compatibility-status');
            const checks = [];

            // 检查 getUserMedia
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                checks.push('<div class="status success">✅ getUserMedia API 支持</div>');
            } else {
                checks.push('<div class="status error">❌ getUserMedia API 不支持</div>');
            }

            // 检查 AudioContext
            if (window.AudioContext || window.webkitAudioContext) {
                checks.push('<div class="status success">✅ Web Audio API 支持</div>');
            } else {
                checks.push('<div class="status error">❌ Web Audio API 不支持</div>');
            }

            // 检查 HTTPS
            if (location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
                checks.push('<div class="status success">✅ 安全上下文（HTTPS/localhost）</div>');
            } else {
                checks.push('<div class="status error">❌ 需要 HTTPS 或 localhost 环境</div>');
            }

            status.innerHTML = checks.join('');
        }

        // 2. 麦克风权限测试
        document.getElementById('test-mic').addEventListener('click', async () => {
            const status = document.getElementById('mic-status');
            status.innerHTML = '<div class="status info">正在请求麦克风权限...</div>';

            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                status.innerHTML = '<div class="status success">✅ 麦克风权限获取成功</div>';
                
                // 停止流
                stream.getTracks().forEach(track => track.stop());
            } catch (error) {
                status.innerHTML = `<div class="status error">❌ 麦克风权限获取失败: ${error.message}</div>`;
            }
        });

        // 3. 音频分析测试
        let audioContext = null;
        let analyser = null;
        let microphone = null;
        let isAnalyzing = false;

        document.getElementById('start-analysis').addEventListener('click', async () => {
            const status = document.getElementById('analysis-status');
            const startBtn = document.getElementById('start-analysis');
            const stopBtn = document.getElementById('stop-analysis');

            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                microphone = audioContext.createMediaStreamSource(stream);
                analyser = audioContext.createAnalyser();
                
                analyser.fftSize = 256;
                const dataArray = new Uint8Array(analyser.frequencyBinCount);
                
                microphone.connect(analyser);
                
                isAnalyzing = true;
                startBtn.disabled = true;
                stopBtn.disabled = false;
                
                status.innerHTML = '<div class="status success">✅ 音频分析已启动</div>';
                
                // 分析循环
                function analyze() {
                    if (!isAnalyzing) return;
                    
                    analyser.getByteFrequencyData(dataArray);
                    
                    let sum = 0;
                    for (let i = 0; i < dataArray.length; i++) {
                        sum += dataArray[i] * dataArray[i];
                    }
                    const rms = Math.sqrt(sum / dataArray.length);
                    const volume = Math.min(100, (rms / 128) * 100);
                    
                    document.getElementById('volume-display').textContent = `音量: ${Math.round(volume)}%`;
                    
                    requestAnimationFrame(analyze);
                }
                
                analyze();
                
            } catch (error) {
                status.innerHTML = `<div class="status error">❌ 音频分析启动失败: ${error.message}</div>`;
            }
        });

        document.getElementById('stop-analysis').addEventListener('click', () => {
            isAnalyzing = false;
            
            if (audioContext) {
                audioContext.close();
                audioContext = null;
            }
            
            document.getElementById('start-analysis').disabled = false;
            document.getElementById('stop-analysis').disabled = true;
            document.getElementById('analysis-status').innerHTML = '<div class="status info">音频分析已停止</div>';
            document.getElementById('volume-display').textContent = '音量: 0%';
        });

        // 页面加载时执行兼容性检测
        checkCompatibility();
    </script>
</body>
</html>
