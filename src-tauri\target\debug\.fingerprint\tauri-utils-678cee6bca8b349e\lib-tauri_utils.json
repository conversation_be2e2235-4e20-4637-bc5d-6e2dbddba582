{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\", \"glob\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"glob\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"toml\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 5011004359846547994, "deps": [[561782849581144631, "html5ever", false, 16516197099120421038], [3150220818285335163, "url", false, 9444140677483369912], [3334271191048661305, "windows_version", false, 17078835838946616460], [4899080583175475170, "semver", false, 7474859038544447637], [5578504951057029730, "serde_with", false, 794020186944709349], [5986029879202738730, "log", false, 8555561636712512847], [6262254372177975231, "kuchiki", false, 14111607461234767741], [6606131838865521726, "ctor", false, 10764790112086548634], [6997837210367702832, "infer", false, 9052763516177243045], [8008191657135824715, "thiserror", false, 12277593154533700771], [9689903380558560274, "serde", false, 13356758597005812676], [10301936376833819828, "json_patch", false, 11627753933910579238], [11989259058781683633, "dunce", false, 10230067452156376648], [14132538657330703225, "brotli", false, 3004841465102247802], [15367738274754116744, "serde_json", false, 14253702188518516403], [15622660310229662834, "walkdir", false, 3859886730018907805], [15932120279885307830, "memchr", false, 13473645001219015354], [17155886227862585100, "glob", false, 8808646221422140041], [17186037756130803222, "phf", false, 16413299716186977422]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-678cee6bca8b349e\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}