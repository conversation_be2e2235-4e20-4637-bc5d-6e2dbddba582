{"rustc": 16591470773350601817, "features": "[\"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 11088512825700362199, "path": 14936214404469579842, "deps": [[7911289239703230891, "adler2", false, 16012443985524511858]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\miniz_oxide-e63792ddba9303e2\\dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}